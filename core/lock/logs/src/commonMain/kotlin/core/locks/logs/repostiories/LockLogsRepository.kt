package core.locks.logs.repostiories

import core.locks.logs.models.LockAccessLog
import core.locks.logs.models.LockLog

interface LockLogsRepository {

    suspend fun log(
        actionType: String,
        lockInfoData: String,
        lockInternalId: String,
        message: String,
        provider: String,
        user: String
    )

    suspend fun lockAccessLog(
        log: LockAccessLog,
        authentication: String
    )

    suspend fun syncLogs(
        internalId: String,
        authentication: String
    ): String

    suspend fun syncAccessLogs(authentication: String)

    suspend fun getLogs(
        internalId: String
    ): List<LockLog>
}